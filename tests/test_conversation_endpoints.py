from uuid import uuid4

from fastapi import status
from httpx import AsyncClient
import pytest

from constants.message import WELCOME_MESSAGE, MessageRole, MessageType
from constants.operation_ids import operation_ids


TEST_MESSAGE_DATA = [
    {
        'role': MessageRole.USER,
        'type': MessageType.TEXT,
        'content': 'User message 1',
    },
    {
        'role': MessageRole.USER,
        'type': MessageType.TEXT,
        'content': 'Assistant response 1',
    },
    {
        'role': MessageRole.USER,
        'type': MessageType.TEXT,
        'content': 'User message 2',
    },
]


async def test_create_conversation_success(
    auth_mock,
    async_client: AsyncClient,
    url_resolver,
    conversation_data: dict,
):
    """Test that a conversation can be created successfully."""
    url = url_resolver.reverse(operation_ids.conversation.CREATE)

    response = await async_client.post(url, headers=auth_mock[0], json=conversation_data)
    data = response.json()

    assert response.status_code == status.HTTP_201_CREATED

    conversation = data['conversation']
    welcome_message = data['welcome_message']
    expected = {
        'conversation': dict(
            **conversation_data,
            created_by_id=auth_mock[1]['oid'],
            created_by_name=auth_mock[1]['name'],
            id=conversation['id'],
            is_completed=False,
            created_at=conversation['created_at'],
        ),
        'welcome_message': {
            'id': welcome_message['id'],
            'conversation_id': conversation['id'],
            'role': str(MessageRole.SYSTEM),
            'type': str(MessageType.TEXT),
            'content': WELCOME_MESSAGE,
            'options': [],
            'created_at': welcome_message['created_at'],
        },
    }
    assert data == expected


async def test_get_conversation_success(
    auth_mock,
    async_client: AsyncClient,
    url_resolver,
    conversation_data: dict,
):
    """Test that a conversation can be retrieved successfully."""
    create_url = url_resolver.reverse(operation_ids.conversation.CREATE)
    create_response = await async_client.post(create_url, headers=auth_mock[0], json=conversation_data)
    conversation_id = create_response.json()['conversation']['id']

    get_url = url_resolver.reverse(operation_ids.conversation.GET, conversation_id=conversation_id)
    get_response = await async_client.get(get_url, headers=auth_mock[0])

    assert get_response.status_code == status.HTTP_200_OK

    data = get_response.json()
    expected_response = dict(
        **conversation_data,
        created_by_id=auth_mock[1]['oid'],
        created_by_name=auth_mock[1]['name'],
        id=conversation_id,
        is_completed=False,
        created_at=data['created_at'],
    )
    assert data == expected_response


async def test_get_conversation_not_found(
    auth_mock,
    async_client: AsyncClient,
    url_resolver,
):
    """Test that a conversation cannot be retrieved if it does not exist."""
    non_existent_id = str(uuid4())
    url = url_resolver.reverse(operation_ids.conversation.GET, conversation_id=non_existent_id)

    response = await async_client.get(url, headers=auth_mock[0])

    assert response.status_code == status.HTTP_404_NOT_FOUND
    expected = {'detail': f'Conversation with ID {non_existent_id} not found'}
    assert response.json() == expected


async def test_get_conversation_invalid_uuid(
    auth_mock,
    async_client: AsyncClient,
    url_resolver,
):
    """Test that a conversation cannot be retrieved if the UUID is invalid."""
    url = url_resolver.reverse(operation_ids.conversation.GET, conversation_id='not-a-uuid')

    response = await async_client.get(url, headers=auth_mock[0])

    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY


@pytest.mark.parametrize(
    'invalid_data',
    (
        {},
        {'qual_id': 'QUAL-123'},
        {'qual_id': 123, 'from_dash': 'not-a-boolean'},
    ),
)
async def test_create_conversation_validation_errors(
    auth_mock,
    async_client: AsyncClient,
    url_resolver,
    invalid_data,
):
    """Test that a conversation cannot be created with invalid data."""
    url = url_resolver.reverse(operation_ids.conversation.CREATE)

    response = await async_client.post(url, headers=auth_mock[0], json=invalid_data)

    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY


async def test_delete_conversation_basic_functionality(
    auth_mock,
    async_client: AsyncClient,
    url_resolver,
    conversation_data: dict,
):
    """Test basic conversation deletion functionality - delete request returns 204 and the conversation no longer exists."""
    create_url = url_resolver.reverse(operation_ids.conversation.CREATE)
    create_response = await async_client.post(create_url, headers=auth_mock[0], json=conversation_data)
    conversation_id = create_response.json()['conversation']['id']

    delete_url = url_resolver.reverse(operation_ids.conversation.DELETE, conversation_id=conversation_id)
    delete_response = await async_client.delete(delete_url, headers=auth_mock[0])

    get_url = url_resolver.reverse(operation_ids.conversation.GET, conversation_id=conversation_id)
    get_response = await async_client.get(get_url, headers=auth_mock[0])

    assert delete_response.status_code == status.HTTP_204_NO_CONTENT
    assert delete_response.content == b''  # No content in response body
    assert get_response.status_code == status.HTTP_404_NOT_FOUND


async def test_delete_conversation_not_found(
    auth_mock,
    async_client: AsyncClient,
    url_resolver,
):
    """Test that a conversation cannot be deleted if it does not exist."""
    non_existent_id = str(uuid4())
    url = url_resolver.reverse(operation_ids.conversation.DELETE, conversation_id=non_existent_id)

    response = await async_client.delete(url, headers=auth_mock[0])

    assert response.status_code == status.HTTP_404_NOT_FOUND
    expected = {'detail': f'Conversation with ID {non_existent_id} not found'}
    assert response.json() == expected


async def test_delete_conversation_invalid_uuid(
    auth_mock,
    async_client: AsyncClient,
    url_resolver,
):
    """Test that a conversation cannot be deleted if the UUID is invalid."""
    url = url_resolver.reverse(operation_ids.conversation.DELETE, conversation_id='not-a-uuid')

    response = await async_client.delete(url, headers=auth_mock[0])

    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY


async def test_get_conversation_messages_success(
    auth_mock,
    async_client: AsyncClient,
    url_resolver,
    conversation_data: dict,
):
    """Test that conversation messages can be retrieved successfully."""
    conversation_url = url_resolver.reverse(operation_ids.conversation.CREATE)
    conversation_response = await async_client.post(conversation_url, headers=auth_mock[0], json=conversation_data)
    conversation_id = conversation_response.json()['conversation']['id']
    welcome_message_id = conversation_response.json()['welcome_message']['id']

    message_create_url = url_resolver.reverse(operation_ids.message.CREATE)
    message_ids = [welcome_message_id]
    for message in TEST_MESSAGE_DATA:
        form_data = {'conversation_id': str(conversation_id), 'content': message['content']}
        response = await async_client.post(message_create_url, headers=auth_mock[0], data=form_data)
        assert response.status_code == status.HTTP_201_CREATED
        message_ids.append(response.json()['user']['id'])

    messages_url = url_resolver.reverse(operation_ids.message.LIST, conversation_id=conversation_id)
    messages_response = await async_client.get(messages_url, headers=auth_mock[0])

    assert messages_response.status_code == status.HTTP_200_OK

    messages = messages_response.json()
    assert len(messages) == 7  # Welcome message + 3 user messages + 3 system messages
    assert messages[0]['id'] == welcome_message_id  # First message should be the welcome message

    # Instead of directly comparing with TEST_MESSAGE_DATA, verify all expected messages are present
    message_contents = [message['content'] for message in messages]
    for test_data in TEST_MESSAGE_DATA:
        assert test_data['content'] in message_contents

    for message in messages:
        assert message['conversation_id'] == conversation_id


async def test_get_conversation_messages_not_found(
    auth_mock,
    async_client: AsyncClient,
    url_resolver,
):
    """Test that conversation messages cannot be retrieved if the conversation does not exist."""
    nonexistent_id = str(uuid4())
    messages_url = url_resolver.reverse(operation_ids.message.LIST, conversation_id=nonexistent_id)

    response = await async_client.get(messages_url, headers=auth_mock[0])

    assert response.status_code == status.HTTP_404_NOT_FOUND


async def test_get_conversation_messages_malformed_uuid(
    auth_mock,
    async_client: AsyncClient,
    url_resolver,
):
    """Test that conversation messages cannot be retrieved if the UUID is invalid."""
    invalid_id = 'not-a-uuid'
    messages_url = url_resolver.reverse(operation_ids.message.LIST, conversation_id=invalid_id)

    response = await async_client.get(messages_url, headers=auth_mock[0])

    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    assert 'detail' in response.json()


async def test_delete_conversation_deletes_all_messages(
    auth_mock,
    async_client: AsyncClient,
    url_resolver,
    conversation_data: dict,
):
    """Test that deleting a conversation also deletes all associated messages."""
    conversation_url = url_resolver.reverse(operation_ids.conversation.CREATE)
    conversation_response = await async_client.post(conversation_url, headers=auth_mock[0], json=conversation_data)
    assert conversation_response.status_code == status.HTTP_201_CREATED
    conversation_id = conversation_response.json()['conversation']['id']

    message_create_url = url_resolver.reverse(operation_ids.message.CREATE)
    for data in TEST_MESSAGE_DATA:
        form_data = {'conversation_id': str(conversation_id), 'content': data['content']}
        response = await async_client.post(message_create_url, headers=auth_mock[0], data=form_data)
        assert response.status_code == status.HTTP_201_CREATED

    messages_url = url_resolver.reverse(operation_ids.message.LIST, conversation_id=conversation_id)
    messages_response = await async_client.get(messages_url, headers=auth_mock[0])

    delete_url = url_resolver.reverse(operation_ids.conversation.DELETE, conversation_id=conversation_id)
    delete_response = await async_client.delete(delete_url, headers=auth_mock[0])

    get_url = url_resolver.reverse(operation_ids.conversation.GET, conversation_id=conversation_id)
    get_response = await async_client.get(get_url, headers=auth_mock[0])

    messages_response_after = await async_client.get(messages_url, headers=auth_mock[0])

    assert messages_response.status_code == status.HTTP_200_OK
    assert len(messages_response.json()) == 7  # 1 welcome + 3 user messages + 3 system messages

    assert delete_response.status_code == status.HTTP_204_NO_CONTENT

    assert get_response.status_code == status.HTTP_404_NOT_FOUND
    assert messages_response_after.status_code == status.HTTP_404_NOT_FOUND
    assert messages_response_after.json()['detail'] == f'Conversation with ID {conversation_id} not found'
