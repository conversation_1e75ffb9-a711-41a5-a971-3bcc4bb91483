from datetime import date, datetime, timezone
import json
from unittest.mock import AsyncMock
from uuid import UUID, uuid4

from fastapi import Depends
import pytest
import pytest_asyncio

from constants.extracted_data import DataSourceType, RequiredField
from dependencies.services import get_extracted_data_service
from repositories import ConversationRepository, ConversationMessageRepository, ExtractedDataRepository
from schemas import (
    ConversationCreationRequest,
    ExtractedData,
    GapAnalysisResult,
    QualGenerationRequest,
)
from services import ExtractedDataService


__all__ = [
    'extracted_data_repository',
    'extracted_data_service',
    'kx_dash_extracted_data',
    'documents_extracted_data',
    'prompt_extracted_data',
    'conversation_id',
    'qual_generation_request',
    'gap_analysis_with_missing_fields',
    'test_conversation',
    'test_user_id',
    'test_user_name',
]

@pytest.fixture
def real_extracted_data_service():
    return Depends(get_extracted_data_service)


# Legacy fixtures for backward compatibility
@pytest.fixture
def extracted_data_repository():
    return AsyncMock()


@pytest.fixture
def extracted_data_service(extracted_data_repository):
    return ExtractedDataService(extracted_data_repository=extracted_data_repository)


@pytest.fixture
def kx_dash_extracted_data():
    return ExtractedData(
        ConversationPublicId=UUID('00000000-0000-0000-0000-000000000001'),
        DataSourceType=DataSourceType.KX_DASH,
        CreatedAt=datetime.now(timezone.utc),
        ActivityName='KX Activity',
        ClientName=json.dumps(['KX Client']),  # type: ignore
        LDMFCountry=json.dumps(['KX Country']),  # type: ignore
        Title='KX Title',
        StartDate=date(2023, 1, 1),
        EndDate=date(2023, 12, 31),
        ClientIndustry=json.dumps({'level_1': 'KX Industry'}),
        ClientServices=json.dumps({'level_1': 'KX Service'}),
        TeamAndRoles=json.dumps({'team_members': ['KX Team Member']}),
        ObjectiveAndScope='KX Objective and Scope',
        Outcomes='KX Outcomes',
    )


@pytest.fixture
def documents_extracted_data():
    return ExtractedData(
        ConversationPublicId=UUID('00000000-0000-0000-0000-000000000001'),
        DataSourceType=DataSourceType.DOCUMENTS,
        CreatedAt=datetime.now(timezone.utc),
        ActivityName='Document Activity',
        ClientName=json.dumps(['Document Client']),  # type: ignore
        LDMFCountry=json.dumps(['Document Country']),  # type: ignore
        Title='Document Title',
        StartDate=date(2023, 2, 1),
        EndDate=date(2023, 11, 30),
        ClientIndustry=json.dumps({'level_1': 'Document Industry'}),
        ClientServices=json.dumps({'level_1': 'Document Service'}),
        TeamAndRoles=json.dumps({'team_members': ['Document Team Member']}),
        ObjectiveAndScope='Document Objective and Scope',
        Outcomes='Document Outcomes',
    )


@pytest.fixture
def prompt_extracted_data():
    return ExtractedData(
        ConversationPublicId=UUID('00000000-0000-0000-0000-000000000001'),
        DataSourceType=DataSourceType.PROMPT,
        CreatedAt=datetime.now(timezone.utc),
        ActivityName='Prompt Activity',
        ClientName=json.dumps(['Prompt Client']),  # type: ignore
        LDMFCountry=json.dumps(['Prompt Country']),  # type: ignore
        Title='Prompt Title',
        StartDate=date(2023, 3, 1),
        EndDate=date(2023, 10, 31),
        ClientIndustry=json.dumps({'level_1': 'Prompt Industry'}),
        ClientServices=json.dumps({'level_1': 'Prompt Service'}),
        TeamAndRoles=json.dumps({'team_members': ['Prompt Team Member']}),
        ObjectiveAndScope='Prompt Objective and Scope',
        Outcomes='Prompt Outcomes',
    )


# New fixtures for qual generation testing
@pytest.fixture
def test_user_id():
    """Provide a test user ID."""
    return uuid4()


@pytest.fixture
def test_user_name():
    """Provide a test user name."""
    return "Test User"


@pytest.fixture
def conversation_id():
    """Provide a test conversation ID."""
    return uuid4()


@pytest_asyncio.fixture
async def test_conversation(
    real_conversation_repository,
    conversation_id,
    test_user_id,
    test_user_name
):
    """Create a test conversation in the database."""
    conversation_data = ConversationCreationRequest(
        qual_id='QUAL-TEST-123',
        from_dash=True
    )

    conversation = await real_conversation_repository.create(
        conversation_data, test_user_id, test_user_name
    )

    # Update the conversation to use our fixed conversation_id
    from sqlalchemy import update
    from models import QualConversation

    await real_conversation_repository.db_session.execute(
        update(QualConversation)
        .where(QualConversation.Id == conversation.Id)
        .values(PublicId=conversation_id)
    )
    await real_conversation_repository.db_session.flush()

    # Update the conversation object to reflect the change
    conversation.PublicId = conversation_id

    return conversation


@pytest.fixture
def qual_generation_request():
    """Provide a test qual generation request."""
    return QualGenerationRequest(prompt="I need help creating a qual for our client engagement.")


@pytest.fixture
def gap_analysis_with_missing_fields():
    """Provide a gap analysis result with missing fields."""
    return GapAnalysisResult(
        missing_fields=[RequiredField.CLIENT_INFO, RequiredField.LDMF_COUNTRY],
        partial_fields=[],
        complete_fields=[RequiredField.ENGAGEMENT_DATES],
    )
