from uuid import uuid4

import pytest
from fastapi import status
from httpx import As<PERSON>Client

from constants.message import MessageRole, MessageType
from constants.operation_ids import operation_ids


class TestQualGenerationAPI:
    @pytest.fixture
    def qual_generation_request_data(self):
        return {"prompt": "I need help creating a qual for our client engagement with ABC Corp."}

    async def test_generate_qual_success_with_missing_data(
        self,
        auth_mock,
        async_client: AsyncClient,
        url_resolver,
        conversation_data: dict,
        qual_generation_request_data,
    ):
        """Test qual generation with missing data returns guidance message."""
        # First create a conversation
        create_url = url_resolver.reverse(operation_ids.conversation.CREATE)
        create_response = await async_client.post(create_url, headers=auth_mock[0], json=conversation_data)
        assert create_response.status_code == status.HTTP_201_CREATED
        conversation_id = create_response.json()['conversation']['id']

        # Execute qual generation
        generate_url = url_resolver.reverse(operation_ids.conversation.GENERATE_QUAL, conversation_id=conversation_id)
        response = await async_client.post(
            generate_url,
            headers=auth_mock[0],
            json=qual_generation_request_data,
        )

        # Verify response
        assert response.status_code == status.HTTP_200_OK

        response_data = response.json()
        assert "user" in response_data
        assert "system" in response_data
        assert response_data["files"] is None

        # Verify user message contains the prompt
        assert response_data["user"]["content"] == qual_generation_request_data["prompt"]
        assert response_data["user"]["role"] == MessageRole.USER
        assert response_data["user"]["type"] == MessageType.TEXT

        # Verify system message contains guidance (since no data is present, should get missing data message)
        assert response_data["system"]["role"] == MessageRole.SYSTEM
        assert response_data["system"]["type"] == MessageType.TEXT
        assert "need some more details" in response_data["system"]["content"].lower()

    async def test_generate_qual_conversation_not_found(
        self,
        auth_mock,
        async_client: AsyncClient,
        url_resolver,
        qual_generation_request_data,
    ):
        """Test qual generation with non-existent conversation returns 404."""
        non_existent_id = str(uuid4())
        generate_url = url_resolver.reverse(operation_ids.conversation.GENERATE_QUAL, conversation_id=non_existent_id)

        response = await async_client.post(
            generate_url,
            headers=auth_mock[0],
            json=qual_generation_request_data,
        )

        assert response.status_code == status.HTTP_404_NOT_FOUND
        assert "Conversation" in response.json()["detail"]

    async def test_generate_qual_invalid_request_data(
        self,
        auth_mock,
        async_client: AsyncClient,
        url_resolver,
        conversation_data: dict,
    ):
        """Test qual generation with invalid request data returns validation error."""
        # First create a conversation
        create_url = url_resolver.reverse(operation_ids.conversation.CREATE)
        create_response = await async_client.post(create_url, headers=auth_mock[0], json=conversation_data)
        conversation_id = create_response.json()['conversation']['id']

        # Execute with missing prompt
        generate_url = url_resolver.reverse(operation_ids.conversation.GENERATE_QUAL, conversation_id=conversation_id)
        response = await async_client.post(
            generate_url,
            headers=auth_mock[0],
            json={},
        )

        # Verify validation error
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    async def test_generate_qual_empty_prompt(
        self,
        auth_mock,
        async_client: AsyncClient,
        url_resolver,
        conversation_data: dict,
    ):
        """Test qual generation with empty prompt is processed successfully."""
        # First create a conversation
        create_url = url_resolver.reverse(operation_ids.conversation.CREATE)
        create_response = await async_client.post(create_url, headers=auth_mock[0], json=conversation_data)
        conversation_id = create_response.json()['conversation']['id']

        # Execute with empty prompt
        generate_url = url_resolver.reverse(operation_ids.conversation.GENERATE_QUAL, conversation_id=conversation_id)
        response = await async_client.post(
            generate_url,
            headers=auth_mock[0],
            json={"prompt": ""},
        )

        # Verify request is processed (empty prompt is valid, service will handle it)
        print(response.json())
        assert response.status_code == status.HTTP_200_OK

        response_data = response.json()
        assert response_data["user"]["content"] == ""
        assert "system" in response_data
