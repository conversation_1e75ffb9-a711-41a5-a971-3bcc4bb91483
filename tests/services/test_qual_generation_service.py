from unittest.mock import AsyncMock
from uuid import UUID

import pytest

from constants.extracted_data import Required<PERSON>ield
from constants.message import MessageR<PERSON>, MessageType
from exceptions import EntityNotFoundError
from schemas import (
    AggregatedData,
    ExtractedAggregationResponse,
    GapAnalysisResult,
    MessageValidator,
    QualGenerationRequest,
    SystemMessageSerializer,
    UserMessageSerializer,
)
from services import ExtractedDataService


class TestExtractedDataServiceQualGeneration:
    @pytest.fixture
    def extracted_data_repository(self):
        return AsyncMock()

    @pytest.fixture
    def conversation_message_repository(self):
        return AsyncMock()

    @pytest.fixture
    def conversation_repository(self):
        return AsyncMock()

    @pytest.fixture
    def extracted_data_service(
        self, extracted_data_repository, conversation_message_repository, conversation_repository
    ):
        return ExtractedDataService(
            extracted_data_repository=extracted_data_repository,
            conversation_message_repository=conversation_message_repository,
            conversation_repository=conversation_repository,
        )

    @pytest.fixture
    def conversation_id(self):
        return UUID('12345678-1234-5678-9012-123456789012')

    @pytest.fixture
    def qual_generation_request(self):
        return QualGenerationRequest(prompt="I need help creating a qual for our client engagement.")

    @pytest.fixture
    def mock_user_message(self):
        mock_message = AsyncMock()
        mock_message.PublicId = UUID('11111111-1111-1111-1111-111111111111')
        mock_message.ConversationPublicId = UUID('12345678-1234-5678-9012-123456789012')
        mock_message.Role = MessageRole.USER
        mock_message.Type = MessageType.TEXT
        mock_message.Content = "I need help creating a qual for our client engagement."
        mock_message.CreatedAt = "2023-01-01T00:00:00Z"
        return mock_message

    @pytest.fixture
    def mock_system_message(self):
        mock_message = AsyncMock()
        mock_message.PublicId = UUID('*************-2222-2222-************')
        mock_message.ConversationPublicId = UUID('12345678-1234-5678-9012-123456789012')
        mock_message.Role = MessageRole.SYSTEM
        mock_message.Type = MessageType.TEXT
        mock_message.Content = ExtractedDataService.MISSING_DATA_MESSAGE
        mock_message.Options = "[]"
        mock_message.CreatedAt = "2023-01-01T00:00:00Z"
        return mock_message

    @pytest.fixture
    def gap_analysis_with_missing_fields(self):
        return ExtractedAggregationResponse(
            aggregated_data=AggregatedData(
                client_name=[],
                ldmf_country=[],
                activity_name='Test Activity',
                title='Test Title',
                start_date='2023-01-01',
                end_date=None,
                client_industry={'level_1': 'Test Industry'},
                client_services={'level_1': 'Test Service'},
                team_and_roles={'team_members': ['Test Team Member']},
                objective_and_scope='Test Objective and Scope',
                outcomes='Test Outcomes',
            ),
            gap_analysis=GapAnalysisResult(
                missing_fields=[RequiredField.CLIENT_INFO, RequiredField.LDMF_COUNTRY],
                partial_fields=[RequiredField.ENGAGEMENT_DATES],
                complete_fields=[RequiredField.OBJECTIVE_SCOPE],
            ),
            recommendations=[],
        )

    async def test_process_qual_generation_request_with_missing_data(
        self,
        extracted_data_repository,
        conversation_id,
        qual_generation_request,
        conversation_repository,
        conversation_message_repository,
        mock_user_message,
        mock_system_message,
        gap_analysis_with_missing_fields,
    ):
        # Create service with mocked dependencies
        service = ExtractedDataService(
            extracted_data_repository=extracted_data_repository,
            conversation_message_repository=conversation_message_repository,
            conversation_repository=conversation_repository,
        )

        # Setup mocks
        conversation_repository.exists.return_value = True
        conversation_message_repository.create.side_effect = [mock_user_message, mock_system_message]
        extracted_data_repository.get.return_value = AggregatedData()  # Return empty data for get
        extracted_data_repository.update.return_value = None
        service.get_aggregated_data_with_gaps = AsyncMock(return_value=gap_analysis_with_missing_fields)

        # Execute
        result = await service.process_qual_generation_request(
            conversation_id, qual_generation_request
        )

        # Verify conversation existence check
        conversation_repository.exists.assert_called_once_with(conversation_id)

        # Verify user message creation
        conversation_message_repository.create.assert_any_call(
            MessageValidator(
                conversation_id=conversation_id,
                role=MessageRole.USER,
                type=MessageType.TEXT,
                content=qual_generation_request.prompt,
            )
        )

        # Verify gap analysis
        service.get_aggregated_data_with_gaps.assert_called_once_with(conversation_id)

        # Verify system message creation with missing data guidance
        conversation_message_repository.create.assert_any_call(
            MessageValidator(
                conversation_id=conversation_id,
                role=MessageRole.SYSTEM,
                type=MessageType.TEXT,
                content=ExtractedDataService.MISSING_DATA_MESSAGE,
                options=[],
            )
        )

        # Verify return types
        assert hasattr(result, 'user')
        assert hasattr(result, 'system')
        assert isinstance(result.user, UserMessageSerializer)
        assert isinstance(result.system, SystemMessageSerializer)

    async def test_process_qual_generation_request_conversation_not_found(
        self, extracted_data_repository, conversation_id, qual_generation_request, conversation_repository, conversation_message_repository
    ):
        # Create service with mocked dependencies
        service = ExtractedDataService(
            extracted_data_repository=extracted_data_repository,
            conversation_message_repository=conversation_message_repository,
            conversation_repository=conversation_repository,
        )

        # Setup mock
        conversation_repository.exists.return_value = False

        # Execute and verify exception
        with pytest.raises(EntityNotFoundError) as exc_info:
            await service.process_qual_generation_request(conversation_id, qual_generation_request)

        assert str(conversation_id) in str(exc_info.value)

    def test_get_next_expected_field_returns_first_priority_field(self, extracted_data_service):
        missing_fields = [RequiredField.OUTCOMES, RequiredField.CLIENT_INFO, RequiredField.LDMF_COUNTRY]

        result = extracted_data_service._get_next_expected_field(missing_fields)

        # Should return the first field in priority order that's missing
        assert result == RequiredField.CLIENT_INFO.value

    def test_get_next_expected_field_returns_first_missing_when_no_priority_match(self, extracted_data_service):
        # Test with fields not in priority order
        missing_fields = [RequiredField.OUTCOMES]

        result = extracted_data_service._get_next_expected_field(missing_fields)

        assert result == RequiredField.OUTCOMES.value

    def test_get_next_expected_field_handles_empty_list(self, extracted_data_service):
        result = extracted_data_service._get_next_expected_field([])

        assert result == "unknown"
