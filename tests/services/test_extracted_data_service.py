import json
from uuid import <PERSON><PERSON><PERSON>

import pytest

from constants.extracted_data import DataSourceType, RequiredField
from constants.message import MessageRole, MessageType
from exceptions import EntityNotFoundError
from schemas import (
    AggregatedData,
    SystemMessageSerializer,
    UserMessageSerializer,
)
from services import ExtractedDataService


class TestExtractedDataService:
    async def test_aggregate_data_prioritizes_sources_correctly(
        self,
        extracted_data_service,
        extracted_data_repository,
        kx_dash_extracted_data,
        documents_extracted_data,
        prompt_extracted_data,
    ):
        # Setup repository to return different data for different sources
        extracted_data_repository.get.side_effect = lambda conversation_id, data_source_type: {
            DataSourceType.KX_DASH: kx_dash_extracted_data,
            DataSourceType.DOCUMENTS: documents_extracted_data,
            DataSourceType.PROMPT: prompt_extracted_data,
        }[data_source_type]

        # Call the method
        result = await extracted_data_service.aggregate_data(UUID('00000000-0000-0000-0000-000000000001'))

        # Verify the result has the highest priority data (from prompt)
        assert result.activity_name == prompt_extracted_data.activity_name
        assert result.client_name == prompt_extracted_data.client_name
        assert result.ldmf_country == prompt_extracted_data.ldmf_country
        assert result.title == prompt_extracted_data.title
        assert result.start_date == prompt_extracted_data.start_date.isoformat()
        assert result.end_date == prompt_extracted_data.end_date.isoformat()
        assert json.dumps(result.client_industry) == prompt_extracted_data.client_industry
        assert json.dumps(result.client_services) == prompt_extracted_data.client_services
        assert json.dumps(result.team_and_roles) == prompt_extracted_data.team_and_roles
        assert result.objective_and_scope == prompt_extracted_data.objective_and_scope
        assert result.outcomes == prompt_extracted_data.outcomes

    async def test_aggregate_data_fills_missing_prompt_fields_from_other_sources(
        self,
        extracted_data_service,
        extracted_data_repository,
        kx_dash_extracted_data,
        documents_extracted_data,
        prompt_extracted_data,
    ):
        # Make prompt_extracted_data incomplete (missing client_name and ldmf_country)
        prompt_extracted_data.client_name = []
        prompt_extracted_data.ldmf_country = []

        # Setup repository to return different data for different sources
        extracted_data_repository.get.side_effect = lambda conversation_id, data_source_type: {
            DataSourceType.KX_DASH: kx_dash_extracted_data,
            DataSourceType.DOCUMENTS: documents_extracted_data,
            DataSourceType.PROMPT: prompt_extracted_data,
        }[data_source_type]

        result = await extracted_data_service.aggregate_data(UUID('00000000-0000-0000-0000-000000000001'))

        # Fields missing in prompt should be filled from documents or kx_dash
        assert result.client_name == documents_extracted_data.client_name or kx_dash_extracted_data.client_name
        assert result.ldmf_country == documents_extracted_data.ldmf_country or kx_dash_extracted_data.ldmf_country
        # Fields present in prompt should still come from prompt
        assert result.activity_name == prompt_extracted_data.activity_name

    async def test_aggregate_data_uses_other_sources_when_prompt_empty(
        self,
        extracted_data_service,
        extracted_data_repository,
        kx_dash_extracted_data,
        documents_extracted_data,
        prompt_extracted_data,
    ):
        # Make prompt_extracted_data empty
        for field in [
            'activity_name',
            'client_name',
            'ldmf_country',
            'title',
            'start_date',
            'end_date',
            'client_industry',
            'client_services',
            'team_and_roles',
            'objective_and_scope',
            'outcomes',
        ]:
            setattr(
                prompt_extracted_data,
                field,
                None if not isinstance(getattr(prompt_extracted_data, field), list) else [],
            )

        extracted_data_repository.get.side_effect = lambda conversation_id, data_source_type: {
            DataSourceType.KX_DASH: kx_dash_extracted_data,
            DataSourceType.DOCUMENTS: documents_extracted_data,
            DataSourceType.PROMPT: prompt_extracted_data,
        }[data_source_type]

        result = await extracted_data_service.aggregate_data(UUID('00000000-0000-0000-0000-000000000001'))

        # All fields should be filled from the next available source (documents or kx_dash)
        assert result.client_name == documents_extracted_data.client_name or kx_dash_extracted_data.client_name
        assert result.activity_name == documents_extracted_data.activity_name or kx_dash_extracted_data.activity_name
        assert result.ldmf_country == documents_extracted_data.ldmf_country or kx_dash_extracted_data.ldmf_country

    async def test_analyze_gaps_identifies_missing_fields(self, extracted_data_service):
        # Create data with some missing fields
        data = AggregatedData(
            client_name=['Test Client'],
            ldmf_country=[],  # Missing
            activity_name='Test Activity',
            title=None,  # Partial objective/scope
            start_date='2023-01-01',
            end_date=None,  # Partial dates
            client_industry={'level_1': 'Test Industry'},
            client_services=None,  # Missing outcomes
            team_and_roles={'team_members': ['Test Team Member']},
        )

        # Call the method
        result = extracted_data_service.analyze_gaps(data)

        # Verify the result
        assert RequiredField.LDMF_COUNTRY in result.missing_fields
        assert RequiredField.OUTCOMES in result.missing_fields
        assert RequiredField.ENGAGEMENT_DATES in result.partial_fields
        assert RequiredField.CLIENT_INFO in result.complete_fields

    async def test_get_aggregated_data_with_gaps_returns_complete_result(
        self, extracted_data_service, extracted_data_repository, kx_dash_extracted_data
    ):
        # Setup repository to return only KX Dash data
        extracted_data_repository.get.side_effect = lambda conversation_id, data_source_type: (
            kx_dash_extracted_data if data_source_type == DataSourceType.KX_DASH else None
        )

        # Call the method
        result = await extracted_data_service.get_aggregated_data_with_gaps(
            UUID('00000000-0000-0000-0000-000000000001')
        )

        assert result.aggregated_data.client_name == ['KX Client']
        assert isinstance(result.recommendations, list)
        if result.recommendations:
            assert result.recommendations[0].field
            assert result.recommendations[0].recommendation


class TestExtractedDataServiceQualGeneration:
    """Test class for qual generation functionality using real database interactions."""

    async def test_process_qual_generation_request_with_missing_data(
        self,
        real_extracted_data_service,
        test_conversation,
        conversation_id,
        qual_generation_request,
        gap_analysis_with_missing_fields,
    ):
        """Test qual generation request processing when data is missing."""
        # Execute the request
        result = await real_extracted_data_service.process_qual_generation_request(
            conversation_id, qual_generation_request
        )

        # Verify return types
        assert hasattr(result, 'user')
        assert hasattr(result, 'system')
        assert isinstance(result.user, UserMessageSerializer)
        assert isinstance(result.system, SystemMessageSerializer)

        # Verify user message content
        assert result.user.content == qual_generation_request.prompt.strip()
        assert result.user.role == MessageRole.USER
        assert result.user.type == MessageType.TEXT

        # Verify system message contains missing data guidance
        assert ExtractedDataService.MISSING_DATA_MESSAGE in result.system.content
        assert result.system.role == MessageRole.SYSTEM
        assert result.system.type == MessageType.TEXT

    async def test_process_qual_generation_request_conversation_not_found(
        self,
        real_extracted_data_service,
        qual_generation_request,
    ):
        """Test qual generation request with non-existent conversation."""
        non_existent_conversation_id = UUID('*************-9999-9999-************')

        with pytest.raises(EntityNotFoundError) as exc_info:
            await real_extracted_data_service.process_qual_generation_request(
                non_existent_conversation_id, qual_generation_request
            )

        assert str(non_existent_conversation_id) in str(exc_info.value)

    def test_get_next_expected_field_returns_first_priority_field(self, real_extracted_data_service):
        """Test that _get_next_expected_field returns the first priority field."""
        missing_fields = [RequiredField.OUTCOMES, RequiredField.CLIENT_INFO, RequiredField.LDMF_COUNTRY]

        result = real_extracted_data_service._get_next_expected_field(missing_fields)

        # Should return the first field in priority order that's missing
        assert result == RequiredField.CLIENT_INFO.value

    def test_get_next_expected_field_returns_first_missing_when_no_priority_match(self, real_extracted_data_service):
        """Test _get_next_expected_field with fields not in priority order."""
        # Test with fields not in priority order
        missing_fields = [RequiredField.OUTCOMES]

        result = real_extracted_data_service._get_next_expected_field(missing_fields)

        assert result == RequiredField.OUTCOMES.value

    def test_get_next_expected_field_handles_empty_list(self, real_extracted_data_service):
        print(real_extracted_data_service, type(real_extracted_data_service))
        """Test _get_next_expected_field with empty list."""
        result = real_extracted_data_service._get_next_expected_field([])

        assert result == "unknown"
