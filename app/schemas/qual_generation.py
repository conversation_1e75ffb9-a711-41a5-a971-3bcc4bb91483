from uuid import UUID

from pydantic import Field

from constants.extracted_data import Required<PERSON>ield
from core.schemas import CustomModel


__all__ = [
    'QualGenerationRequest',
    'QualGenerationResponse',
    'MissingDataResponse',
]


class QualGenerationRequest(CustomModel):
    """Schema for qual generation request."""
    
    prompt: str = Field(description="User prompt for qual generation")


class MissingDataResponse(CustomModel):
    """Schema for missing data response when qual generation cannot proceed."""
    
    status: str = Field(default="missing_data_required", description="Status indicating missing data")
    message: str = Field(description="Initial guidance message for the user")
    next_expected_field: str = Field(description="The next field expected to be collected")
    missing_fields: list[RequiredField] = Field(description="List of missing required fields")


class QualGenerationResponse(CustomModel):
    """Schema for qual generation response."""
    
    # Union type will be handled by discriminator in the future if needed
    # For now, we'll use optional fields to represent different response types
    
    # Missing data response fields
    status: str | None = Field(default=None, description="Response status")
    message: str | None = Field(default=None, description="Response message")
    next_expected_field: str | None = Field(default=None, description="Next expected field")
    missing_fields: list[RequiredField] | None = Field(default=None, description="Missing required fields")
    
    # Future: qual generation success fields would go here
    # qual_content: str | None = Field(default=None, description="Generated qual content")
    # completion_status: str | None = Field(default=None, description="Completion status")
