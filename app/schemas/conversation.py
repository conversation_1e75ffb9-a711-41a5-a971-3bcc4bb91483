from datetime import datetime
from uuid import UUID

from pydantic import ConfigDict, Field

from core.schemas import CustomModel
from schemas.conversation_message import SystemMessageSerializer


__all__ = [
    'ConversationCreationRequest',
    'ConversationResponse',
    'ConversationWithWelcomeMessageResponse',
]


class ConversationCreationRequest(CustomModel):
    """Schema for creating a new conversation."""

    qual_id: str
    from_dash: bool


class ConversationResponse(CustomModel):
    """Schema for conversation response."""

    id: UUID = Field(validation_alias='PublicId')
    qual_id: str = Field(validation_alias='QualId')
    from_dash: bool = Field(validation_alias='FromDash')
    is_completed: bool = Field(validation_alias='IsCompleted')
    created_at: datetime = Field(validation_alias='CreatedAt')
    created_by_id: UUID = Field(validation_alias='CreatedById')
    created_by_name: str | None = Field(validation_alias='CreatedByName')

    model_config = ConfigDict(
        from_attributes=True,
    )


class ConversationWithWelcomeMessageResponse(CustomModel):
    """Schema for conversation with welcome message response."""

    conversation: ConversationResponse
    welcome_message: SystemMessageSerializer
