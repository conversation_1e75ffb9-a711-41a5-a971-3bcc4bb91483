from datetime import date, datetime, timezone
import json
from typing import Self
from uuid import UUID

from pydantic import ConfigDict, Field, field_validator

from constants.extracted_data import DataSourceType, RequiredField
from core.schemas import CustomModel


class ExtractedData(CustomModel):
    conversation_id: UUID = Field(alias='ConversationPublicId')
    qual_conversation_id: int | None = Field(default=None, alias='QualConversationId')
    data_source_type: DataSourceType = Field(alias='DataSourceType')
    created_at: datetime = Field(alias='CreatedAt')

    activity_name: str | None = Field(default=None, alias='ActivityName')
    client_name: list[str] = Field(default=[], alias='ClientName')
    ldmf_country: list[str] = Field(default=[], alias='LDMFCountry')
    title: str | None = Field(default=None, alias='Title')
    start_date: date | None = Field(default=None, alias='StartDate')
    end_date: date | None = Field(default=None, alias='EndDate')
    client_industry: str | None = Field(default=None, alias='ClientIndustry')
    client_services: str | None = Field(default=None, alias='ClientServices')
    team_and_roles: str | None = Field(default=None, alias='TeamAndRoles')
    objective_and_scope: str | None = Field(default=None,alias='ObjectiveAndScope')
    outcomes: str | None = Field(default=None,alias='Outcomes')

    model_config = ConfigDict(
        from_attributes=True,
    )

    @field_validator('client_name', mode='before')
    @classmethod
    def validate_client_name(cls, value: str | None) -> list[str]:
        return [] if value is None else json.loads(value)

    @field_validator('ldmf_country', mode='before')
    @classmethod
    def validate_ldmf_country(cls, value: str | None) -> list[str]:
        return [] if value is None else json.loads(value)

    @classmethod
    def create(cls, conversation_id: UUID, data_source_type: DataSourceType, qual_conversation_id: int | None = None) -> Self:
        return cls.model_validate(
            {
                'conversation_id': conversation_id,
                'data_source_type': data_source_type,
                'qual_conversation_id': qual_conversation_id,
                'created_at': datetime.now(timezone.utc),
            }
        )

    def model_dump_for_db(self) -> dict:
        result = super().model_dump(by_alias=True)
        for field_name in ('ConversationPublicId', 'CreatedAt'):
            result.pop(field_name, None)
        for field_name in ('ClientName', 'LDMFCountry', 'ClientServices', 'TeamAndRoles'):
            if (value := result.get(field_name)) is not None:
                result[field_name] = json.dumps(value)
        return result


class GapAnalysisResult(CustomModel):
    """Result of a gap analysis on extracted data."""

    missing_fields: list[RequiredField] = Field(default_factory=list)
    partial_fields: list[RequiredField] = Field(default_factory=list)
    complete_fields: list[RequiredField] = Field(default_factory=list)


class AggregatedData(CustomModel):
    """Aggregated data from all sources."""

    client_name: list[str] = Field(default_factory=list)
    ldmf_country: list[str] = Field(default_factory=list)
    activity_name: str | None = None
    title: str | None = None
    start_date: str | None = None
    end_date: str | None = None
    client_industry: dict | None = None
    client_services: dict | None = None
    team_and_roles: dict | None = None
    objective_and_scope: str | None = None
    outcomes: str | None = None


class FieldRecomendation(CustomModel):
    field: RequiredField
    recommendation: str


class ExtractedAggregationResponse(CustomModel):
    aggregated_data: AggregatedData
    gap_analysis: GapAnalysisResult
    recommendations: list[FieldRecomendation]
