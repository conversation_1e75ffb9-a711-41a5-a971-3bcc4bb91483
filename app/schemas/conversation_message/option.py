from datetime import date
from typing import Literal

from pydantic import Field, field_serializer

from constants.message import OptionType
from core.schemas import CustomModel


__all__ = [
    'ClientNameOption',
    'LDMFCountryOption',
    'KXDashTaskOption',
    'DatePickerOption',
    'Option',
]


class ClientNameOption(CustomModel):
    type: Literal[OptionType.CLIENT_NAME] = Field(default=OptionType.CLIENT_NAME, frozen=True)
    client_name: str


class LDMFCountryOption(CustomModel):
    type: Literal[OptionType.LDMF_COUNTRY] = Field(default=OptionType.LDMF_COUNTRY, frozen=True)
    ldmf_country: str


class KXDashTaskOption(CustomModel):
    type: Literal[OptionType.KX_DASH_TASK] = Field(default=OptionType.KX_DASH_TASK, frozen=True)
    activity_id: int
    client_name: str
    engagement_code: str


class DatePickerOption(CustomModel):
    type: Literal[OptionType.DATES] = Field(default=OptionType.DATES, frozen=True)
    start_date: date | None
    end_date: date | None

    serialize_start_date = field_serializer('start_date')(CustomModel.serialize_date)
    serialize_end_date = field_serializer('end_date')(CustomModel.serialize_date)


Option = ClientNameOption | LDMFCountryOption | KXDashTaskOption | DatePickerOption
