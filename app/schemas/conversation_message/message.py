from datetime import datetime
from typing import Annotated, Sequence
from uuid import UUI<PERSON>

from fastapi import UploadFile
from pydantic import ConfigD<PERSON>, Field, field_validator

from config import settings
from constants.message import MessageRole, MessageType, OptionType
from core import json
from core.schemas import CustomModel

from ..document import DocumentResponse
from .option import Option


__all__ = [
    'MessageValidator',
    'BaseMessageSerializer',
    'UserMessageSerializer',
    'SystemMessageSerializer',
    'MessageSerializer',
    'CombinedMessageSerializer',
]


class MessageValidator(CustomModel):
    """Schema for creating a new conversation message."""

    conversation_id: UUID
    role: MessageRole = Field(serialization_alias='Role')
    type: MessageType = Field(serialization_alias='Type')
    content: str = Field(serialization_alias='Content')
    options: Sequence[Option] = Field(default=[], serialization_alias='Options')  # NOTE: For user messages only
    selected_option: Option | None = Field(
        default=None, serialization_alias='SelectedOption'
    )  # NOTE: For system messages only
    files: list[UploadFile] | None = None

    @field_validator('files')
    @classmethod
    def validate_files(cls, files: list[UploadFile] | None) -> list[UploadFile] | None:
        for file in files or ():
            # Validate file has a name
            if not file.filename:
                raise ValueError('File must have a name')

            # Validate filename has content before extension
            filename = file.filename
            name_part = filename.rsplit('.', 1)[0] if '.' in filename else filename
            if not name_part:
                raise ValueError(f'File "{filename}" must have a name before extension')

            # Validate file size
            file_size = file.size or 0
            if file_size > settings.document_storage.max_file_size:
                max_size_expected = settings.document_storage.max_file_size
                msg = f'{file} size {file_size} bytes exceeds maximum allowed size of {max_size_expected} bytes'
                raise ValueError(msg)

            # Validate file type
            content_type = file.content_type
            if not content_type:
                raise ValueError(f'File "{file.filename}" must have a content type')

            file_formats_set = settings.document_storage.supported_file_formats
            if content_type not in file_formats_set:
                supported_formats = ', '.join(file_formats_set)
                msg = f'{file} has wrong type {content_type}. Expected {supported_formats}'
                raise ValueError(msg)

        return files

    def model_dump_for_db(self) -> dict:
        result = super().model_dump(by_alias=True)
        for field_name in ('conversation_id', 'files'):
            result.pop(field_name, None)
        for field_name in ('Options', 'SelectedOption'):
            result[field_name] = None if (field_value := result[field_name]) is None else json.dumps(field_value)
        return result


class BaseMessageSerializer(CustomModel):
    id: UUID = Field(validation_alias='PublicId')
    conversation_id: UUID = Field(validation_alias='ConversationPublicId')
    role: MessageRole = Field(validation_alias='Role')
    type: MessageType = Field(validation_alias='Type')
    content: str = Field(validation_alias='Content')
    created_at: datetime = Field(validation_alias='CreatedAt')

    model_config = ConfigDict(
        from_attributes=True,
    )


class UserMessageSerializer(BaseMessageSerializer):
    selected_option: Option | None = Field(discriminator='type', validation_alias='SelectedOption')

    @field_validator('selected_option', mode='before')
    @classmethod
    def validate_selected_option(cls, value: str | None) -> dict | None:
        if value is None:
            return None

        option = json.loads(value)
        option['type'] = OptionType(option['type'])
        return option


class SystemMessageSerializer(BaseMessageSerializer):
    options: Sequence[Annotated[Option, Field(discriminator='type')]] = Field(validation_alias='Options')

    @field_validator('options', mode='before')
    @classmethod
    def validate_options(cls, value: str) -> list[dict]:
        options = json.loads(value)
        for option in options:
            option['type'] = OptionType(option['type'])
        return options


MessageSerializer = UserMessageSerializer | SystemMessageSerializer


class CombinedMessageSerializer(CustomModel):
    """Schema for conversation messages response."""

    user: UserMessageSerializer
    system: SystemMessageSerializer
    files: list[DocumentResponse] | None = None
