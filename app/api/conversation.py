import logging
from typing import Sequence, cast
from uuid import UUID

from fastapi import APIRouter, HTTPException, Request, status

from constants.message import WELCOME_MESSAGE, MessageRole, MessageType
from constants.operation_ids import operation_ids
from dependencies import ConversationMessageServiceDep, ConversationServiceDep, ExtractedDataServiceDep
from exceptions import EntityNotFoundError
from schemas import (
    BaseMessageSerializer,
    CombinedMessageSerializer,
    ConversationCreationRequest,
    ConversationResponse,
    ConversationWithWelcomeMessageResponse,
    MessageSerializer,
    MessageValidator,
    QualGenerationRequest,
    SystemMessageSerializer,
)


__all__ = ['router']

logger = logging.getLogger(__name__)

router = APIRouter(prefix='/conversations')


@router.post(
    '',
    operation_id=operation_ids.conversation.CREATE,
    status_code=status.HTTP_201_CREATED,
)
async def create_conversation_with_welcome_message(
    conversation_data: ConversationCreationRequest,
    conversation_service: ConversationServiceDep,
    message_service: ConversationMessageServiceDep,
    request: Request,
) -> ConversationWithWelcomeMessageResponse:
    """
    Create a new conversation with a welcome system message.

    This endpoint creates a new conversation and automatically adds a system welcome message.
    """
    # TODO: When Dash task support is added, implement passing of Dash task data to conversation
    try:
        conversation = await conversation_service.create(
            conversation_data, user_id=request.state.user.id, user_name=request.state.user.full_name
        )

        welcome_message = await message_service.create_message(
            MessageValidator(
                conversation_id=conversation.id,
                role=MessageRole.SYSTEM,
                type=MessageType.TEXT,
                content=WELCOME_MESSAGE,
            )
        )

        return ConversationWithWelcomeMessageResponse(
            conversation=conversation,
            welcome_message=cast(SystemMessageSerializer, welcome_message),
        )
    except Exception as e:
        logger.error('Error creating conversation with welcome message: %s', str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f'Failed to create conversation with welcome message: {str(e)}',
        ) from e


@router.get('/{conversation_id}', operation_id=operation_ids.conversation.GET)
async def get_conversation_by_id(
    conversation_id: UUID,
    conversation_service: ConversationServiceDep,
) -> ConversationResponse:
    """
    Get a conversation by its ID.
    """
    try:
        conversation = await conversation_service.get(conversation_id)
        return conversation
    except EntityNotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e),
        )
    except Exception as e:
        logger.error('Error retrieving conversation: %s', e)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail='Failed to retrieve conversation',
        )


@router.get(
    '/{conversation_id}/messages',
    operation_id=operation_ids.message.LIST,
    status_code=status.HTTP_200_OK,
    response_model=Sequence[MessageSerializer],
)
async def list_messages(
    conversation_id: UUID,
    message_service: ConversationMessageServiceDep,
) -> Sequence[BaseMessageSerializer]:
    """
    Get all messages for a specific conversation.
    """
    try:
        return await message_service.list(conversation_id)

    except EntityNotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e),
        )

    except Exception as e:
        logger.error('Error retrieving messages for conversation: %s', e)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f'Failed to retrieve conversation messages: {str(e)}',
        )


@router.delete(
    '/{conversation_id}',
    operation_id=operation_ids.conversation.DELETE,
    status_code=status.HTTP_204_NO_CONTENT,
)
async def delete_conversation(
    conversation_id: UUID,
    conversation_service: ConversationServiceDep,
) -> None:
    """
    Delete a conversation by its ID.

    This operation also deletes all messages associated with the conversation.
    """
    try:
        await conversation_service.delete(conversation_id)
    except EntityNotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e),
        )
    except Exception as e:
        logger.error('Error deleting conversation: %s', e)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail='Failed to delete conversation',
        )


@router.post(
    '/{conversation_id}/generate-qual',
    operation_id=operation_ids.conversation.GENERATE_QUAL,
    status_code=status.HTTP_200_OK,
)
async def generate_qual(
    conversation_id: UUID,
    request: QualGenerationRequest,
    extracted_data_service: ExtractedDataServiceDep,
) -> CombinedMessageSerializer:
    """
    Generate a qual based on user prompt and detect missing required data.

    This endpoint processes a user prompt for qual generation and returns:
    - If missing data is detected: guidance message with next steps
    - If all data is present: generated qual content (future implementation)

    The response includes both the user message (with the prompt) and
    the system response (guidance or qual content).
    """
    try:
        return await extracted_data_service.process_qual_generation_request(conversation_id, request)

    except EntityNotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e),
        )
    except Exception as e:
        logger.error('Error processing qual generation request: %s', e)
        print(e)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail='Failed to process qual generation request',
        )
