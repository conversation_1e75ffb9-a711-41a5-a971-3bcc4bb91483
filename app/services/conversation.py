import logging
from uuid import UUI<PERSON>

from exceptions import EntityNotFoundError
from repositories import ConversationRepository
from schemas import ConversationCreationRequest, ConversationResponse

from .conversation_message import ConversationMessageService
from .document import DocumentService
from .extracted_data import ExtractedDataService


__all__ = ['ConversationService']


logger = logging.getLogger(__name__)


class ConversationService:
    """Service for conversation-related business logic."""

    def __init__(
        self,
        conversation_repository: ConversationRepository,
        conversation_message_service: ConversationMessageService,
        extracted_data_service: ExtractedDataService,
        document_service: DocumentService,
    ):
        self.conversation_repository = conversation_repository
        self.conversation_message_service = conversation_message_service
        self.extracted_data_service = extracted_data_service
        self.document_service = document_service

    async def create(
        self, conversation_data: ConversationCreationRequest, user_id: UUID, user_name: str
    ) -> ConversationResponse:
        """
        Create a new conversation.

        Args:
            conversation_data: Data for creating the conversation

        Returns:
            Response with the created conversation data

        Raises:
            DatabaseException: If there's an error creating the conversation
        """
        try:
            logger.debug('Creating new conversation for Qual ID: %s', conversation_data.qual_id)
            conversation = await self.conversation_repository.create(
                conversation_data=conversation_data, user_id=user_id, user_name=user_name
            )
            return ConversationResponse.model_validate(conversation, from_attributes=True)
        except Exception as e:
            logger.error('Error creating conversation: %s', e)
            raise

    async def get(self, public_id: UUID) -> ConversationResponse:
        """
        Get a conversation by its ID.

        Args:
            public_id: The public ID of the conversation

        Returns:
            The conversation response

        Raises:
            EntityNotFoundError: If the conversation doesn't exist
            DatabaseException: If there's an error retrieving the conversation
        """
        try:
            logger.debug('Retrieving conversation with ID: %s', public_id)
            conversation = await self.conversation_repository.get(public_id)

            if not conversation:
                raise EntityNotFoundError('Conversation', str(public_id))

            return ConversationResponse.model_validate(conversation, from_attributes=True)
        except Exception as e:
            logger.error('Error retrieving conversation: %s', e)
            raise

    async def delete(self, conversation_id: UUID) -> None:
        """
        Delete a conversation by ID.

        Args:
            conversation_id: UUID of the conversation to delete

        Raises:
            EntityNotFoundError: If conversation with the given ID does not exist
        """
        try:
            logger.debug('Deleting conversation with ID: %s', conversation_id)
            await self.document_service.delete_many(conversation_id)
            await self.conversation_message_service.delete_many(conversation_id)
            await self.extracted_data_service.delete_many(conversation_id)
            await self.conversation_repository.delete(conversation_id)
            logger.info('Successfully deleted conversation with ID: %s', conversation_id)
        except EntityNotFoundError:
            logger.warning('Conversation with ID %s not found for deletion', conversation_id)
            raise
        except Exception as e:
            logger.error('Failed to delete conversation %s: %s', conversation_id, e, exc_info=True)
            raise
