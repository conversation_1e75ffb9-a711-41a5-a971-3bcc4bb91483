import json
import logging
from typing import Any
from uuid import UUID

from constants.extracted_data import DataSourceType, RequiredField
from constants.message import MessageRole, MessageType
from exceptions import EntityNotFoundError
from repositories import ConversationMessageRepository, ConversationRepository, ExtractedDataRepository
from schemas import (
    AggregatedData,
    CombinedMessageSerializer,
    ExtractedAggregationResponse,
    ExtractedData,
    FieldRecomendation,
    GapAnalysisResult,
    MessageValidator,
    QualGenerationRequest,
    SystemMessageSerializer,
    UserMessageSerializer,
)

from .parsers import DocumentsDataParser, KXDashDataParser, PromptDataParser


__all__ = ['ExtractedDataService']


logger = logging.getLogger(__name__)


class ExtractedDataService:
    _SOURCE_TYPE_TO_PARSER_MAP = {
        DataSourceType.KX_DASH: KXDashDataParser,
        DataSourceType.DOCUMENTS: DocumentsDataParser,
        DataSourceType.PROMPT: PromptDataParser,
    }

    # Define the required fields for a complete qual
    _REQUIRED_FIELDS = {
        RequiredField.CLIENT_INFO,
        RequiredField.LDMF_COUNTRY,
        RequiredField.ENGAGEMENT_DATES,
        RequiredField.OBJECTIVE_SCOPE,
        RequiredField.OUTCOMES,
    }

    # Initial guidance message when missing data is detected
    MISSING_DATA_MESSAGE = (
        "I'll need some more details in order to create a draft qual. "
        "Could you start by telling me about the client and what services we delivered?"
    )

    # Field priority order for sequential data collection
    FIELD_COLLECTION_ORDER = [
        RequiredField.CLIENT_INFO,
        RequiredField.LDMF_COUNTRY,
        RequiredField.ENGAGEMENT_DATES,
        RequiredField.OBJECTIVE_SCOPE,
        RequiredField.OUTCOMES,
    ]

    def __init__(
        self,
        extracted_data_repository: ExtractedDataRepository,
        conversation_message_repository: ConversationMessageRepository | None = None,
        conversation_repository: ConversationRepository | None = None,
    ):
        self.extracted_data_repository = extracted_data_repository
        self.conversation_message_repository = conversation_message_repository
        self.conversation_repository = conversation_repository

    async def delete_many(self, conversation_id: UUID) -> None:
        """
        Delete all extracted data for a conversation.

        Args:
            conversation_id: The ID of the conversation to delete extracted data from.

        Raises:
            EntityNotFoundError: If the conversation does not exist.
        """
        logger.debug('Deleting extracted data for conversation ID: %s', conversation_id)
        try:
            await self.extracted_data_repository.delete_many(conversation_id)
        except Exception as e:
            logger.error("Failed to delete extracted data for conversation ID '%s': %s", conversation_id, e)
            raise

    async def update(
        self, conversation_id: UUID, raw_data: dict[str, Any], source_type: DataSourceType
    ) -> ExtractedData:
        """
        Update extracted data for a conversation.

        Args:
            conversation_id: The ID of the conversation to update extracted data for.
            raw_data: The data to update the extracted data with.
            source_type: The type of data source being updated.

        Raises:
            EntityNotFoundError: If the conversation does not exist.
            ServiceExceptionError: If the extracted data record is not found.
        """
        logger.debug('Started updating "%s" extracted data for conversation ID "%s"', source_type, conversation_id)
        try:
            extracted_data = await self.extracted_data_repository.get(
                conversation_id=conversation_id,
                data_source_type=source_type,
            )

            if not extracted_data:
                # Get internal conversation ID for creating new extracted data
                if not self.conversation_repository:
                    raise ValueError("Conversation repository must be configured")

                internal_conversation_id = await self.conversation_repository.get_internal_id(conversation_id)
                if internal_conversation_id is None:
                    raise EntityNotFoundError('Conversation', str(conversation_id))

                extracted_data = ExtractedData.create(
                    conversation_id=conversation_id,
                    data_source_type=source_type,
                    qual_conversation_id=internal_conversation_id
                )

            parser = self._SOURCE_TYPE_TO_PARSER_MAP[source_type]()
            await self.extracted_data_repository.update(parser(extracted_data, raw_data))
            return extracted_data

        except Exception as e:
            logger.error(
                'Failed to update "%s" extracted data for conversation ID "%s": %s', source_type, conversation_id, e
            )
            raise

    async def aggregate_data(self, conversation_id: UUID) -> AggregatedData:
        """
        Aggregate data from all sources for a conversation, applying the priority order:
        1. KX Dash data (lowest priority - base data)
        2. Document extracted data (medium priority - overrides KX Dash)
        3. User prompt data (highest priority - overrides all other sources)

        Args:
            conversation_id: The ID of the conversation to aggregate data for.

        Returns:
            AggregatedData: The aggregated data from all sources.

        Raises:
            EntityNotFoundError: If the conversation does not exist.
        """
        # Initialize with empty values
        aggregated_data = {
            'client_name': [],
            'ldmf_country': [],
            'activity_name': None,
            'title': None,
            'start_date': None,
            'end_date': None,
            'client_industry': None,
            'client_services': None,
            'team_and_roles': None,
            'objective_and_scope': None,
            'outcomes': None,
        }

        # Process data sources in priority order (lowest to highest)
        for source_type in [DataSourceType.KX_DASH, DataSourceType.DOCUMENTS, DataSourceType.PROMPT]:
            try:
                extracted_data = await self.extracted_data_repository.get(
                    conversation_id=conversation_id,
                    data_source_type=source_type,
                )

                if not extracted_data:
                    logger.debug('No %s data found for conversation %s', source_type, conversation_id)
                    continue

                # Update with non-empty values from this source
                if extracted_data.client_name:
                    aggregated_data['client_name'] = extracted_data.client_name

                if extracted_data.ldmf_country:
                    aggregated_data['ldmf_country'] = extracted_data.ldmf_country

                if extracted_data.activity_name:
                    aggregated_data['activity_name'] = extracted_data.activity_name

                if extracted_data.title:
                    aggregated_data['title'] = extracted_data.title

                if extracted_data.start_date:
                    aggregated_data['start_date'] = (
                        extracted_data.start_date.isoformat() if extracted_data.start_date else None
                    )

                if extracted_data.end_date:
                    aggregated_data['end_date'] = (
                        extracted_data.end_date.isoformat() if extracted_data.end_date else None
                    )

                if extracted_data.client_industry:
                    try:
                        aggregated_data['client_industry'] = json.loads(extracted_data.client_industry)
                    except (json.JSONDecodeError, TypeError):
                        logger.warning('Failed to parse client_industry JSON for conversation %s', conversation_id)

                if extracted_data.client_services:
                    try:
                        aggregated_data['client_services'] = json.loads(extracted_data.client_services)
                    except (json.JSONDecodeError, TypeError):
                        logger.warning('Failed to parse client_services JSON for conversation %s', conversation_id)

                if extracted_data.team_and_roles:
                    try:
                        aggregated_data['team_and_roles'] = json.loads(extracted_data.team_and_roles)
                    except (json.JSONDecodeError, TypeError):
                        logger.warning('Failed to parse team_and_roles JSON for conversation %s', conversation_id)

                if extracted_data.objective_and_scope:
                    aggregated_data['objective_and_scope'] = extracted_data.objective_and_scope

                if extracted_data.outcomes:
                    aggregated_data['outcomes'] = extracted_data.outcomes

            except Exception as e:
                logger.warning('Error processing %s data for conversation %s: %s', source_type, conversation_id, e)
                continue

        return AggregatedData(**aggregated_data)

    def analyze_gaps(self, data: AggregatedData) -> GapAnalysisResult:
        """
        Analyze gaps in the aggregated data against required fields.

        Args:
            data: The aggregated data to analyze.

        Returns:
            GapAnalysisResult: The result of the gap analysis.
        """
        missing_fields = []
        partial_fields = []
        complete_fields = []

        # Check client information
        if not data.client_name:
            missing_fields.append(RequiredField.CLIENT_INFO)
        elif not data.client_industry:
            partial_fields.append(RequiredField.CLIENT_INFO)
        else:
            complete_fields.append(RequiredField.CLIENT_INFO)

        # Check LDMF country
        if not data.ldmf_country:
            missing_fields.append(RequiredField.LDMF_COUNTRY)
        else:
            complete_fields.append(RequiredField.LDMF_COUNTRY)

        # Check engagement dates
        if not data.start_date and not data.end_date:
            missing_fields.append(RequiredField.ENGAGEMENT_DATES)
        elif not data.start_date or not data.end_date:
            partial_fields.append(RequiredField.ENGAGEMENT_DATES)
        else:
            complete_fields.append(RequiredField.ENGAGEMENT_DATES)

        # Check objective and scope
        if not data.objective_and_scope:
            missing_fields.append(RequiredField.OBJECTIVE_SCOPE)
        else:
            complete_fields.append(RequiredField.OBJECTIVE_SCOPE)

        # Check outcomes
        if not data.outcomes:
            missing_fields.append(RequiredField.OUTCOMES)
        else:
            complete_fields.append(RequiredField.OUTCOMES)

        return GapAnalysisResult(
            missing_fields=missing_fields,
            partial_fields=partial_fields,
            complete_fields=complete_fields,
        )

    async def get_aggregated_data_with_gaps(self, conversation_id: UUID) -> ExtractedAggregationResponse:
        """
        Get aggregated data and gap analysis for a conversation.

        Args:
            conversation_id: The ID of the conversation to get data for.

        Returns:
            Dict containing:
            - aggregated_data: The aggregated data from all sources
            - gap_analysis: Analysis of missing, partial, and complete fields
            - recommendations: List of fields to request from the user next

        Raises:
            EntityNotFoundError: If the conversation does not exist.
        """
        aggregated_data = await self.aggregate_data(conversation_id)
        gap_analysis = self.analyze_gaps(aggregated_data)

        # Generate recommendations based on gap analysis
        recommendations = []

        # First recommend missing fields
        for field in gap_analysis.missing_fields:
            recommendations.append(self._get_recommendation_for_field(field))

        # Then recommend partial fields
        for field in gap_analysis.partial_fields:
            recommendations.append(self._get_recommendation_for_field(field, partial=True))

        return ExtractedAggregationResponse(
            aggregated_data=aggregated_data,
            gap_analysis=gap_analysis,
            recommendations=recommendations,
        )

    @staticmethod
    def _get_recommendation_for_field(field: RequiredField, partial: bool = False) -> FieldRecomendation:
        """
        Get a recommendation for a missing or partial field.

        Args:
            field: The field to get a recommendation for.
            partial: Whether the field is partially complete.

        Returns:
            Dict containing field name and recommendation text.
        """
        recommendations = {
            RequiredField.CLIENT_INFO: {
                False: 'Provide client name and industry information',
                True: 'Complete client information with industry details',
            },
            RequiredField.LDMF_COUNTRY: {
                False: 'Provide the Lead Deloitte Member Firm country',
                True: 'Confirm the Lead Deloitte Member Firm country',
            },
            RequiredField.ENGAGEMENT_DATES: {
                False: 'Provide engagement start and end dates',
                True: 'Complete the missing engagement date',
            },
            RequiredField.OBJECTIVE_SCOPE: {
                False: 'Provide the engagement title and activity name',
                True: 'Complete the engagement objective and scope',
            },
            RequiredField.OUTCOMES: {
                False: 'Provide information about the engagement outcomes and services delivered',
                True: 'Provide more details about the engagement outcomes',
            },
        }

        return FieldRecomendation(
            field=field,
            recommendation=recommendations[field][partial],
        )

    async def process_qual_generation_request(
        self, conversation_id: UUID, request: QualGenerationRequest
    ) -> CombinedMessageSerializer:
        """
        Process a qual generation request and return appropriate response.

        Args:
            conversation_id: UUID of the conversation
            request: The qual generation request containing user prompt

        Returns:
            CombinedMessageSerializer containing both user and system messages

        Raises:
            EntityNotFoundError: If the conversation doesn't exist
            ValueError: If required repositories are not configured
        """
        if not self.conversation_message_repository or not self.conversation_repository:
            raise ValueError("Conversation repositories must be configured for qual generation")

        logger.debug('Processing qual generation request for conversation: %s', conversation_id)

        # Verify conversation exists
        if not await self.conversation_repository.exists(conversation_id):
            raise EntityNotFoundError('Conversation', str(conversation_id))

        # Create user message with the prompt
        user_message_data = MessageValidator(
            conversation_id=conversation_id,
            role=MessageRole.USER,
            type=MessageType.TEXT,
            content=request.prompt.strip(),
        )

        user_message = await self.conversation_message_repository.create(user_message_data)

        # Store prompt data for analysis using existing update method
        await self._store_prompt_data(conversation_id, request.prompt)

        # Analyze for missing data using existing method
        gap_analysis_result = await self.get_aggregated_data_with_gaps(conversation_id)

        # Generate system response based on gap analysis
        if gap_analysis_result.gap_analysis.missing_fields:
            system_message = await self._create_missing_data_response(
                conversation_id, gap_analysis_result.gap_analysis.missing_fields
            )
        else:
            # Future: Handle case where all data is present and qual can be generated
            system_message = await self._create_placeholder_response(conversation_id)

        return CombinedMessageSerializer(
            user=UserMessageSerializer.model_validate(user_message, from_attributes=True),
            system=SystemMessageSerializer.model_validate(system_message, from_attributes=True),
            files=None,  # No files for qual generation responses
        )

    async def _store_prompt_data(self, conversation_id: UUID, prompt: str) -> None:
        """
        Store the user prompt as extracted data for analysis using existing update method.

        Args:
            conversation_id: UUID of the conversation
            prompt: User prompt text
        """
        # Store the raw prompt in the objective_and_scope field
        # In the future, this could be enhanced with LLM-based extraction
        prompt_data = {
            'objective_and_scope': prompt,
        }

        await self.update(conversation_id, prompt_data, DataSourceType.PROMPT)

    async def _create_missing_data_response(
        self, conversation_id: UUID, missing_fields: list[RequiredField]
    ) -> SystemMessageSerializer:
        """
        Create a system message response for missing data scenario.

        Args:
            conversation_id: UUID of the conversation
            missing_fields: List of missing required fields

        Returns:
            Created system message
        """
        if not self.conversation_message_repository:
            raise ValueError("Conversation message repository must be configured")

        # Create system message with guidance
        system_message_data = MessageValidator(
            conversation_id=conversation_id,
            role=MessageRole.SYSTEM,
            type=MessageType.TEXT,
            content=self.MISSING_DATA_MESSAGE,
            # Future: Add options for guided data collection
            options=[],
        )

        system_message = await self.conversation_message_repository.create(system_message_data)

        logger.info(
            'Created missing data response for conversation %s with %d missing fields',
            conversation_id,
            len(missing_fields),
        )

        return SystemMessageSerializer.model_validate(system_message, from_attributes=True)

    async def _create_placeholder_response(self, conversation_id: UUID) -> SystemMessageSerializer:
        """
        Create a placeholder system message for when all data is present.
        This is a temporary implementation until full qual generation is implemented.

        Args:
            conversation_id: UUID of the conversation

        Returns:
            Created system message
        """
        if not self.conversation_message_repository:
            raise ValueError("Conversation message repository must be configured")

        system_message_data = MessageValidator(
            conversation_id=conversation_id,
            role=MessageRole.SYSTEM,
            type=MessageType.TEXT,
            content="Thank you for providing the information. Qual generation will be implemented in a future update.",
            options=[],
        )

        system_message = await self.conversation_message_repository.create(system_message_data)
        return SystemMessageSerializer.model_validate(system_message, from_attributes=True)

    def _get_next_expected_field(self, missing_fields: list[RequiredField]) -> str:
        """
        Determine the next field to collect based on priority order.

        Args:
            missing_fields: List of missing required fields

        Returns:
            String identifier of the next field to collect
        """
        for field in self.FIELD_COLLECTION_ORDER:
            if field in missing_fields:
                return field.value

        # Fallback to first missing field if none match priority order
        return missing_fields[0].value if missing_fields else "unknown"
