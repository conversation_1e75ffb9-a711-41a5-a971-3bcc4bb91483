import datetime as dt
from functools import partial
import json
from typing import Any


__all__ = ['CustomJSONEncoder', 'dump', 'dumps', 'load', 'loads']


class CustomJSONEncoder(json.JSONEncoder):
    def default(self, o: Any) -> Any:
        if isinstance(o, dt.date):
            return o.isoformat()

        return super().default(o)


dump = partial(json.dump, cls=CustomJSONEncoder)
dumps = partial(json.dumps, cls=CustomJSONEncoder)
load = json.load
loads = json.loads
